<html lang="cn">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>洋葱学园</title>
    <script
      type="text/javascript"
      src="//fp.yangcong345.com/middle/2.16.5/onion-utils.min.js"
    ></script>
    <script
      type="text/javascript"
      src="//fp.yangcong345.com/middle/1.9.0/axios.min.js"
    ></script>
    <!-- PageSpy SDK -->
    <script
      crossorigin="anonymous"
      src="https://pagespy.yc345.tv/page-spy/index.min.js"
    ></script>

    <!-- 插件（非必须，但建议使用） -->
    <script
      crossorigin="anonymous"
      src="https://pagespy.yc345.tv/plugin/data-harbor/index.min.js"
    ></script>
    <script
      crossorigin="anonymous"
      src="https://pagespy.yc345.tv/plugin/rrweb/index.min.js"
    ></script>
    <script type="text/javascript">
      window.onload = function () {
        const env = "{ENV}"
        const link = document.querySelector('a')

        if (link.textContent !== '打开微信') {
          link.style.backgroundColor = 'gray' // 当内容不等于"打开微信"时，将颜色设置为灰色
          link.style.pointerEvents = 'none' // 禁止点击事件
          link.style.margin = '60px 0 auto'

          const reason = document.createElement('span')
          reason.textContent = '{reason}'
          reason.style.display = 'block'
          reason.style.marginTop = '10px'
          reason.style.color = 'gray'

          link.parentNode.insertBefore(reason, link.nextSibling) // 将span插入到a标签后面
          link.parentNode.style.display = 'flex'
          link.parentNode.style.flexDirection = 'column'
          link.parentNode.style.alignItems = 'center'
        }
        link.style.display = 'block'

        try {
          if (link.textContent === '打开微信') {
            onionUtils.setBrowserNavHidden(false)

            setTimeout(function () {
              window.location.href = '{targetURL}'
            }, 300)
          }
        } catch (e) {
          console.log('当前环境不支持设置导航：', e)
        }

      // 增加埋点上报的逻辑js
      const resetPageSpy = () => {
        window.$harbor = new DataHarborPlugin()
        window.$rrweb = new RRWebPlugin()

        ;[window.$harbor, window.$rrweb].forEach((p) => {
          PageSpy.registerPlugin(p)
        })

        window.$pageSpy = new PageSpy({
          api: 'pagespy.yc345.tv',
          clientOrigin: 'https://pagespy.yc345.tv',
          project: 'telesale-wecom',
          title: 'telesale-wecom-page',
        })
      }

      if (env === 'test' || env === 'stage') {
        resetPageSpy()
      }

      // 发送埋点上报
      const sendPoint = async () => {
        const getQueryObject = onionUtils.getQueryObject()
        const envMap = {
          master: 'production',
          stage: 'stage',
          test: 'development',
        }
        const points = {
          enterOpenWechatTransitionPage: {
            category: 'site',
            data: ['fromPageName', 'taskId'], // taskId:渠道ID
            desc: '洋葱学园APP跳转微信，进入【打开微信】页面',
          },
          getOpenWechatResults: {
            category: 'site',
            data: ['installationId', 'taskId'], // taskId:渠道ID
            desc: '洋葱学园APP跳转微信，回传微信打开结果',
          }
        }

        const point = new onionUtils.BuryPoint(points, {
          contextData: {
            productId: '41',
            u_user: getQueryObject.uid || getQueryObject.userId || '',
          },
          env: envMap[env] || 'development',
          axios,
        })

        point.h5Post('enterOpenWechatTransitionPage', {
          fromPageName: getQueryObject.fromPageName || getQueryObject.scene || '',
          taskId: onionUtils.getQueryString('channelId'),
        })

        let isWechatInstall = 'unknown'

        if (onionUtils.isInNative()) {
          try {
            const res = await onionUtils.callNative('isWechatInstall')
            console.log('isWechatInstall', res.installed);
            isWechatInstall = res.installed ? 'installed' : 'notInstalled'
          } catch (e) {
            console.error('调用原生方法 isWechatInstall 失败:', e)
          }
        }

        point.h5Post('getOpenWechatResults', {
          installationId: isWechatInstall,
          taskId: onionUtils.getQueryString('channelId'),
        })
      }

      sendPoint()
    }
    </script>
    <style type="text/css">
      a {
        width: 200px;
        margin: 60px auto;
        text-align: center;
        padding: 10px 5px;
        background-color: #1584ff;
        font-size: 18px;
        color: white;
        display: none;
        border-radius: 5px;
        text-decoration: none;
      }

      .img {
        width: 6.358968rem;
        height: 6.358968rem;
        margin: 8.444432rem auto 0;
        overflow: hidden;
      }
    </style>
  </head>
  <body>
    <div class="img">
      <img
        style="width: 100%"
        src="https://assets.weibanzhushou.com/web/we-work-webapp/external-links-drainage_wechat-logo.1e970b881e3f.png"
      />
    </div>
    <a href="{targetURL}">{buttonText}</a>
  </body>
</html>