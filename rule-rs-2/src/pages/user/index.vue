<!--
  * @Date         : 2025-04-27 16:13:58
  * @Description  :
  * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const { query } = useRoute()
const chartRef = ref<HTMLDivElement>()

// 雷达图数据
const radarData = [
  { name: '基础消防', value: 12, color: '#ff9999' },
  { name: '物联网消防', value: 19, color: '#99ccff' },
  { name: '消安一体化', value: 5, color: '#99ff99' },
  { name: '设施设备', value: 3, color: '#ffcc99' }
]

const initChart = () => {
  if (!chartRef.value) return

  const chart = echarts.init(chartRef.value)

  const option = {
    title: {
      text: '系统维度',
      left: 20,
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    radar: {
      center: ['35%', '55%'],
      radius: '60%',
      indicator: radarData.map(item => ({
        name: item.name,
        max: 20
      })),
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        show: true,
        fontSize: 10,
        color: '#999'
      }
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: radarData.map(item => item.value),
            areaStyle: {
              color: 'rgba(153, 204, 255, 0.3)'
            },
            lineStyle: {
              color: '#66b3ff',
              width: 2
            },
            itemStyle: {
              color: '#66b3ff'
            }
          }
        ]
      }
    ]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<template>
  <div class="user-page">
    <h1>User Page</h1>
    <div>带过来的参数{{ query }}</div>

    <div class="radar-container">
      <div class="chart-wrapper">
        <div ref="chartRef" class="radar-chart"></div>

        <!-- 右侧图例 -->
        <div class="legend-panel">
          <h3>消防名称</h3>
          <div class="legend-list">
            <div
              v-for="item in radarData"
              :key="item.name"
              class="legend-item"
            >
              <div
                class="legend-color"
                :style="{ backgroundColor: item.color }"
              ></div>
              <span class="legend-name">{{ item.name }}</span>
              <span class="legend-value">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-page {
  padding: 20px;
}

.radar-container {
  margin-top: 30px;
  border: 2px solid #ff4444;
  padding: 20px;
  background: white;
}

.chart-wrapper {
  display: flex;
  align-items: center;
  gap: 40px;
}

.radar-chart {
  width: 400px;
  height: 300px;
  flex-shrink: 0;
}

.legend-panel {
  flex: 1;
  min-width: 200px;
}

.legend-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.legend-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.legend-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  min-width: 30px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-wrapper {
    flex-direction: column;
    gap: 20px;
  }

  .radar-chart {
    width: 100%;
    max-width: 400px;
    height: 250px;
  }
}
</style>
