<!--
  * @Date         : 2025-04-27 16:13:58
  * @Description  :
  * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const { query } = useRoute()
const chartRef = ref<HTMLDivElement>()

// 雷达图数据
const radarData = [
  { name: '基础消防', value: 12, color: '#ff9999' },
  { name: '物联网消防', value: 19, color: '#99ccff' },
  { name: '消安一体化', value: 5, color: '#99ff99' },
  { name: '设施设备', value: 3, color: '#ffcc99' }
]

const initChart = () => {
  if (!chartRef.value) return

  const chart = echarts.init(chartRef.value)

  // 计算数据点的位置
  const centerX = 200
  const centerY = 200
  const maxRadius = 120

  // 为每个数据项计算角度和半径
  const dataPoints = radarData.map((item, index) => {
    // 将数据分布在圆周上，每个占90度
    const angle = (index * 90) * Math.PI / 180
    const radius = (item.value / 20) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    return { x, y, ...item }
  })

  const option = {
    title: {
      text: '系统维度',
      left: 20,
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    xAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 400
    },
    yAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 400
    },
    series: [
      // 同心圆背景
      ...Array.from({ length: 6 }, (_, i) => ({
        type: 'scatter',
        data: [[centerX, centerY]],
        symbol: 'circle',
        symbolSize: (i + 1) * 40,
        itemStyle: {
          color: 'transparent',
          borderColor: i === 5 ? 'transparent' : '#e8f4fd',
          borderWidth: 1
        },
        silent: true,
        z: 1
      })),
      // 扇形区域 - 基础消防 (右上，粉色)
      {
        type: 'custom',
        data: [1],
        renderItem: () => ({
          type: 'sector',
          shape: {
            cx: centerX,
            cy: centerY,
            r0: 0,
            r: (12 / 20) * maxRadius,
            startAngle: 0,
            endAngle: Math.PI / 2
          },
          style: {
            fill: 'rgba(255, 182, 193, 0.6)'
          }
        }),
        z: 2
      },
      // 扇形区域 - 物联网消防 (右下，蓝色)
      {
        type: 'custom',
        data: [1],
        renderItem: () => ({
          type: 'sector',
          shape: {
            cx: centerX,
            cy: centerY,
            r0: 0,
            r: (19 / 20) * maxRadius,
            startAngle: -Math.PI / 2,
            endAngle: 0
          },
          style: {
            fill: 'rgba(173, 216, 230, 0.6)'
          }
        }),
        z: 2
      },
      // 扇形区域 - 消安一体化 (左下，绿色)
      {
        type: 'custom',
        data: [1],
        renderItem: () => ({
          type: 'sector',
          shape: {
            cx: centerX,
            cy: centerY,
            r0: 0,
            r: (5 / 20) * maxRadius,
            startAngle: Math.PI,
            endAngle: Math.PI * 1.5
          },
          style: {
            fill: 'rgba(144, 238, 144, 0.6)'
          }
        }),
        z: 2
      },
      // 扇形区域 - 设施设备 (左上，橙色)
      {
        type: 'custom',
        data: [1],
        renderItem: () => ({
          type: 'sector',
          shape: {
            cx: centerX,
            cy: centerY,
            r0: 0,
            r: (3 / 20) * maxRadius,
            startAngle: Math.PI / 2,
            endAngle: Math.PI
          },
          style: {
            fill: 'rgba(255, 218, 185, 0.6)'
          }
        }),
        z: 2
      },
      // 数据点
      {
        type: 'scatter',
        data: dataPoints.map(p => [p.x, p.y]),
        symbol: 'circle',
        symbolSize: 12,
        itemStyle: {
          color: '#1890ff'
        },
        z: 3
      }
    ]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<template>
  <div class="user-page">
    <h1>User Page</h1>
    <div>带过来的参数{{ query }}</div>

    <div class="radar-container">
      <div class="chart-wrapper">
        <div ref="chartRef" class="radar-chart"></div>

        <!-- 右侧图例 -->
        <div class="legend-panel">
          <h3>消防名称</h3>
          <div class="legend-list">
            <div
              v-for="item in radarData"
              :key="item.name"
              class="legend-item"
            >
              <div
                class="legend-color"
                :style="{ backgroundColor: item.color }"
              ></div>
              <span class="legend-name">{{ item.name }}</span>
              <span class="legend-value">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-page {
  padding: 20px;
}

.radar-container {
  margin-top: 30px;
  border: 2px solid #ff4444;
  padding: 20px;
  background: white;
}

.chart-wrapper {
  display: flex;
  align-items: center;
  gap: 40px;
}

.radar-chart {
  width: 400px;
  height: 300px;
  flex-shrink: 0;
}

.legend-panel {
  flex: 1;
  min-width: 200px;
}

.legend-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.legend-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.legend-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  min-width: 30px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-wrapper {
    flex-direction: column;
    gap: 20px;
  }

  .radar-chart {
    width: 100%;
    max-width: 400px;
    height: 250px;
  }
}
</style>
