{"name": "rule-rs-2", "private": true, "version": "1.0.0", "scripts": {"dev": "rsbuild dev --env-mode development --open", "build:test": "rsbuild build --env-mode test", "build:stage": "rsbuild build --env-mode stage", "build:prod": "rsbuild build --env-mode production", "preview": "rsbuild preview", "prepare": "husky install"}, "dependencies": {"@guanghe-pub/axios-interceptors": "^0.1.1", "@guanghe-pub/onion-oss-webpack-plugin": "^1.2.0", "axios": "^1.7.2", "echarts": "^5.6.0", "element-plus": "^2.8.7", "pinia": "^2.1.7", "vue": "^3.4.19", "vue-router": "^4.3.0"}, "devDependencies": {"@commitlint/config-conventional": "^18.4.3", "@guanghe-pub/eslint-config-base": "^0.0.3", "@guanghe-pub/eslint-config-vue3": "0.0.2-beta.28", "@guanghe-pub/stylelint-config": "0.0.2-beta.28", "@rsbuild/core": "^0.5.2", "@rsbuild/plugin-babel": "^0.6.5", "@rsbuild/plugin-vue": "^0.5.2", "@rsbuild/plugin-vue-jsx": "^0.6.5", "@types/eslint": "8.56.0", "@types/node": "^20.12.7", "@typescript-eslint/eslint-plugin": "6.16.0", "@typescript-eslint/parser": "6.16.0", "commitlint": "18.4.3", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.2", "eslint-plugin-vue": "9.19.2", "husky": "8.0.3", "lint-staged": "15.2.0", "postcss-scss": "4.0.9", "prettier": "3.1.1", "stylelint": "15.11.0", "stylelint-config-html": "1.1.0", "stylelint-config-recess-order": "4.4.0", "stylelint-config-standard": "34.0.0", "stylelint-order": "6.0.4", "stylelint-prettier": "4.1.0", "typescript": "^5.4.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0", "pnpm": ">=8.0.0", "yarn": ">=1.22.0"}}